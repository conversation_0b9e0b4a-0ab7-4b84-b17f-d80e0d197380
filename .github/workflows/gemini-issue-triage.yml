name: Gemini Issue Triage

on:
  issues:
    types: [opened, reopened]

jobs:
  triage-issue:
    if: github.event_name == 'issues'
    permissions:
      contents: read
      issues: write
    concurrency:
      group: ${{ github.workflow }}-${{ github.event.issue.number }}
      cancel-in-progress: true
    runs-on: ubuntu-latest
    steps:
      - name: Generate GitHub App Token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.PRIVATE_KEY }}

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate_token.outputs.token }}

      - name: Run Gemini Issue Triage
        uses: google-gemini/gemini-cli-action@238438ee44e83c7f97a1a9bb61e62853cebe9767
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
        with:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          prompt: |
            You are an issue triage assistant for GitHub issues.
            Your task is to analyze the issue and apply appropriate labels from the repository's list of available labels.

            **IMPORTANT: Your only action should be to apply labels. Do not post any comments or modify any code.**

            **Triage Workflow:**

            1.  **Fetch Available Labels:**
                - Execute the following shell command to get the list of all labels in the repository:
                  `gh label list`

            2.  **Analyze the Issue:**
                - Based on the issue title, body, and any comments, determine the most appropriate labels.
                - Always apply:
                  - Kind of issue (e.g., `kind/bug`, `kind/enhancement`, `kind/documentation`)
                  - Area of the codebase it affects (e.g., `area/core`, `area/ux`, `area/tools`)
                  - Priority if possible (e.g., `priority/p1`, `priority/p2`).

            3.  **Apply Labels:**
                - Use the `gh` command-line tool to add the selected labels to the issue.
                - Example command: `gh issue edit ${{ github.event.issue.number }} --add-label "kind/bug,area/core"`
                - You can add multiple labels in a single command.

            4.  **Finalize:**
                - Remove the `status/need-triage` label if it exists, as the issue has been triaged.

            **Guidelines:**
            - Only use labels that exist in the repository (from the `gh label list` command).
            - Do not add a comment to the issue.
            - If no labels seem appropriate, do not apply any.

            **Issue Information:**
            - Repository: ${{ github.repository }}
            - Issue Number: ${{ github.event.issue.number }}
            - Issue Title: ${{ github.event.issue.title }}
            - Issue Body: ${{ github.event.issue.body }}
            - Comment (if any): ${{ github.event.comment.body }}
