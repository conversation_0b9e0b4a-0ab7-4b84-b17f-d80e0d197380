/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import { GenerateContentResponse, FinishReason, Content, Part, GenerateContentParameters, FunctionCall, GenerateContentConfig, Tool } from '@google/genai';
import { Turn } from './turn.js';
import { LLMProvider } from './llm.js';
import axios from 'axios';
import { Config } from '../config/config.js';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';

// DeepSeek Chat instance that mimics GeminiChat interface
class DeepSeekChat {
  private history: Content[] = [];
  private systemPrompt: string = '';

  constructor(
    private apiKey: string,
    private model: string,
    private generationConfig: GenerateContentConfig = {},
    systemPrompt?: string
  ) {
    this.systemPrompt = systemPrompt || '';
  }

  addHistory(content: Content): void {
    this.history.push(content);
  }

  getHistory(): Content[] {
    return [...this.history];
  }

  setHistory(history: Content[]): void {
    this.history = [...history];
  }

  async sendMessage(params: { message: string | Part | Part[]; config?: { abortSignal?: AbortSignal; tools?: Tool[] } }): Promise<GenerateContentResponse> {
    // Use sendMessageStream and get the first (and only) result
    const stream = await this.sendMessageStream(params);
    const result = await stream.next();
    return result.value;
  }

  async sendMessageStream(params: { message: string | Part | Part[]; config?: { abortSignal?: AbortSignal; tools?: Tool[] } }): Promise<AsyncGenerator<GenerateContentResponse>> {

    // Convert message to string
    let messageText: string;
    if (typeof params.message === 'string') {
      messageText = params.message;
    } else if (Array.isArray(params.message)) {
      messageText = params.message.map(part => 'text' in part ? part.text : '').join('');
    } else if ('text' in params.message) {
      messageText = params.message.text || '';
    } else {
      messageText = '';
    }

    // Add user message to history
    this.addHistory({
      role: 'user',
      parts: [{ text: messageText }]
    });

    // Convert history to DeepSeek format
    const messages: Array<{role: string, content: string}> = [];

    // Add system prompt if available
    if (this.systemPrompt.trim()) {
      messages.push({
        role: 'system',
        content: this.systemPrompt
      });
    }

    // Add conversation history
    this.history.forEach(content => {
      const role = content.role === 'model' ? 'assistant' : (content.role || 'user');
      const text = (content.parts || []).map(part => 'text' in part ? (part.text || '') : '').join('');
      if (text.trim()) {
        messages.push({
          role,
          content: text
        });
      }
    });



    // Convert tools to DeepSeek format if provided
    // Check both params.config.tools and this.generationConfig.tools (like Gemini)
    let tools: Array<{ type: string; function: { name: string; description: string; parameters: unknown } }> | undefined;

    const configTools = params.config?.tools || this.generationConfig?.tools;
    if (configTools && Array.isArray(configTools) && configTools.length > 0) {
      tools = this.convertGeminiToolsToDeepSeekFormat(configTools);
    }

    try {
      const requestBody: {
        model: string;
        messages: Array<{ role: string; content: string }>;
        stream: boolean;
        tools?: Array<{ type: string; function: { name: string; description: string; parameters: unknown } }>;
      } = {
        model: 'deepseek-chat', // Always use DeepSeek model name
        messages,
        stream: false, // For now, we'll implement non-streaming
      };

      // Add tools if available
      if (tools && tools.length > 0) {
        requestBody.tools = tools;
      }

      const response = await axios.post(
        DEEPSEEK_API_URL,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          signal: params.config?.abortSignal,
        }
      );

      const deepseekResponse = response.data;

      // Convert DeepSeek response to Gemini format
      const geminiResponse = this.convertDeepSeekResponseToGeminiFormat(deepseekResponse);

      // Add assistant response to history
      const assistantContent = geminiResponse.candidates?.[0]?.content;
      if (assistantContent) {
        this.addHistory(assistantContent);
      }

      // Return as async generator
      return (async function* () {
        yield geminiResponse;
      })();

    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error(
          `DeepSeek API authentication failed. ` +
          `Environment variable DEEPSEEK_API_KEY: ${process.env.DEEPSEEK_API_KEY ? 'SET' : 'NOT SET'}. ` +
          `Please check your API key.`
        );
      }
      throw error;
    }
  }

  /**
   * Convert Gemini tools format to DeepSeek tools format
   */
  private convertGeminiToolsToDeepSeekFormat(geminiTools: unknown[]): Array<{ type: string; function: { name: string; description: string; parameters: unknown } }> {
    return geminiTools.map((tool: unknown) => {
      if (tool && typeof tool === 'object' && 'functionDeclarations' in tool) {
        const toolObj = tool as { functionDeclarations: Array<{ name?: string; description?: string; parameters?: unknown }> };
        // Handle Gemini function declarations
        return toolObj.functionDeclarations.map((func) => ({
          type: 'function',
          function: {
            name: func.name || '',
            description: func.description || '',
            parameters: func.parameters || { type: 'object', properties: {} }
          }
        }));
      }
      return [];
    }).flat();
  }

  /**
   * Convert DeepSeek API response to Gemini format
   */
  private convertDeepSeekResponseToGeminiFormat(deepseekResponse: {
    choices?: Array<{
      message?: {
        content?: string;
        tool_calls?: Array<{
          id: string;
          type: string;
          function: {
            name: string;
            arguments: string;
          };
        }>;
      };
    }>;
    usage?: {
      prompt_tokens?: number;
      completion_tokens?: number;
      total_tokens?: number;
    };
  }): GenerateContentResponse {
    const choice = deepseekResponse.choices?.[0];
    const message = choice?.message;
    const content = message?.content || '';
    const toolCalls = message?.tool_calls;

    // Build parts array
    const parts: Part[] = [];

    // Add text content if available
    if (content) {
      parts.push({ text: content });
    }

    // Convert tool calls to function calls for the top-level functionCalls field
    const functionCalls: FunctionCall[] = [];
    if (toolCalls && Array.isArray(toolCalls)) {
      for (const toolCall of toolCalls) {
        if (toolCall.type === 'function' && toolCall.function) {
          const functionCall = {
            id: toolCall.id,
            name: toolCall.function.name,
            args: typeof toolCall.function.arguments === 'string'
              ? JSON.parse(toolCall.function.arguments)
              : toolCall.function.arguments
          };

          functionCalls.push(functionCall);

          // Also add to parts for compatibility
          parts.push({
            functionCall
          });
        }
      }
    }

    // Create response object with all fields
    const response: GenerateContentResponse = {
      candidates: [
        {
          content: {
            parts: parts.length > 0 ? parts : [{ text: content }],
            role: 'model',
          },
          finishReason: FinishReason.STOP,
          index: 0,
          safetyRatings: [],
        },
      ],
      usageMetadata: {
        promptTokenCount: deepseekResponse.usage?.prompt_tokens || 0,
        candidatesTokenCount: deepseekResponse.usage?.completion_tokens || 0,
        totalTokenCount: deepseekResponse.usage?.total_tokens || 0,
      },
      promptFeedback: {
        safetyRatings: [],
      },
      functionCalls: functionCalls.length > 0 ? functionCalls : undefined,
      text: undefined,
      data: undefined,
      executableCode: undefined,
      codeExecutionResult: undefined,
    };

    return response;
  }
}

export class DeepSeekClient implements LLMProvider {
  private apiKey: string;
  private chat?: DeepSeekChat;
  private model: string;
  private generateContentConfig: GenerateContentConfig = {
    temperature: 0,
    topP: 1,
  };

  constructor(private config: Config) {
    this.apiKey = this.config.getProviders()?.deepseek?.apiKey || '';
    // For DeepSeek, always use deepseek-chat as the model name for display
    // The actual API call will use 'deepseek-chat' regardless
    this.model = 'deepseek-chat';


  }

  // GeminiClient-compatible methods
  async initialize(contentConfig?: any): Promise<void> {
    // Initialize the chat with system prompt
    await this.startChat();
  }

  getChat(): DeepSeekChat {
    if (!this.chat) {
      // Auto-initialize if not already done
      // This is a synchronous fallback - we'll create a basic chat without full initialization
      this.chat = new DeepSeekChat(this.apiKey, this.model, {}, '');
    }
    return this.chat;
  }

  private async getEnvironment(): Promise<Part[]> {
    const cwd = this.config.getWorkingDir();
    const today = new Date().toLocaleDateString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    const platform = process.platform;
    const { getFolderStructure } = await import('../utils/getFolderStructure.js');
    const folderStructure = await getFolderStructure(cwd, {
      fileService: this.config.getFileService(),
    });
    const context = `
  This is the Gemini CLI. We are setting up the context for our chat.
  Today's date is ${today}.
  My operating system is: ${platform}
  I'm currently working in the directory: ${cwd}
  ${folderStructure}
          `.trim();

    const initialParts: Part[] = [{ text: context }];
    const toolRegistry = await this.config.getToolRegistry();

    // Add full file context if the flag is set
    if (this.config.getFullContext()) {
      try {
        const { ReadManyFilesTool } = await import('../tools/read-many-files.js');
        const readManyFilesTool = toolRegistry.getTool(
          'read_many_files',
        ) as InstanceType<typeof ReadManyFilesTool>;
        if (readManyFilesTool) {
          // Read all files in the target directory
          const result = await readManyFilesTool.execute(
            {
              paths: ['**/*'], // Read everything recursively
              useDefaultExcludes: true, // Use default excludes
            },
            AbortSignal.timeout(30000),
          );
          if (result.llmContent) {
            initialParts.push({
              text: `\n--- Full File Context ---\n${result.llmContent}`,
            });
          } else {
            console.warn(
              'Full context requested, but read_many_files returned no content.',
            );
          }
        } else {
          console.warn(
            'Full context requested, but read_many_files tool not found.',
          );
        }
      } catch (error) {
        // Not using reportError here as it's a startup/config phase, not a chat/generation phase error.
        console.error('Error reading full file context:', error);
        initialParts.push({
          text: '\n--- Error reading full file context ---',
        });
      }
    }

    return initialParts;
  }

  // Method to update system prompt for existing chat
  updateSystemPrompt(systemPrompt: string): void {
    if (this.chat) {
      (this.chat as any).systemPrompt = systemPrompt;
    }
  }

  // Add startChat method for compatibility with GeminiClient interface
  async startChat(extraHistory?: Content[]): Promise<DeepSeekChat> {
    // Get environment context like GeminiClient does
    const envParts = await this.getEnvironment();

    // Import getCoreSystemPrompt dynamically to avoid circular dependency
    const { getCoreSystemPrompt } = await import('./prompts.js');

    const userMemory = this.config.getUserMemory();
    console.log('🔍 DeepSeek userMemory length:', userMemory.length);
    console.log('🔍 DeepSeek userMemory contains GEMINI.md:', userMemory.includes('GEMINI.md'));
    console.log('🔍 DeepSeek userMemory contains Vitest:', userMemory.includes('Vitest'));
    console.log('🔍 DeepSeek userMemory contains "Building and running":', userMemory.includes('Building and running'));
    if (userMemory.length > 0) {
      console.log('🔍 DeepSeek userMemory preview (first 300 chars):');
      console.log(userMemory.substring(0, 300));
    }
    const systemInstruction = getCoreSystemPrompt(userMemory);

    // Get tools like GeminiClient does
    const toolRegistry = await this.config.getToolRegistry();
    const toolDeclarations = toolRegistry.getFunctionDeclarations();
    const tools = [{ functionDeclarations: toolDeclarations }];
    console.log('DeepSeekClient.startChat() - tools count:', toolDeclarations.length);

    // Create generation config with tools (like GeminiClient)
    const generationConfig = {
      ...this.generateContentConfig,
      tools,
    };

    // Create initial history like GeminiClient does
    const initialHistory: Content[] = [
      {
        role: 'user',
        parts: envParts,
      },
      {
        role: 'assistant',
        parts: [{ text: 'Got it. Thanks for the context!' }],
      },
    ];
    const history = initialHistory.concat(extraHistory ?? []);

    // Create a new chat with system prompt and tools
    this.chat = new DeepSeekChat(this.apiKey, this.model, generationConfig, systemInstruction);

    // Add initial history to match GeminiClient behavior
    history.forEach(content => {
      this.chat!.addHistory(content);
    });

    return this.chat;
  }

  async addHistory(content: Content): Promise<void> {
    this.getChat().addHistory(content);
  }

  async getHistory(): Promise<Content[]> {
    return this.getChat().getHistory();
  }

  async setHistory(history: Content[]): Promise<void> {
    this.getChat().setHistory(history);
  }

  async resetChat(): Promise<void> {
    // Preserve the system prompt when resetting
    const currentSystemPrompt = this.chat ? (this.chat as any).systemPrompt : '';
    this.chat = new DeepSeekChat(this.apiKey, this.model, currentSystemPrompt);
  }

  async generateContent(history: Turn[]): Promise<GenerateContentResponse> {
    if (!this.apiKey) {
      const envKey = process.env.DEEPSEEK_API_KEY;
      const configProviders = this.config.getProviders();
      throw new Error(
        `DEEPSEEK_API_KEY is not configured.\n` +
        `Environment variable DEEPSEEK_API_KEY: ${envKey ? 'SET' : 'NOT SET'}\n` +
        `Config providers: ${JSON.stringify(configProviders, null, 2)}\n` +
        `Please set the DEEPSEEK_API_KEY environment variable.`
      );
    }

    // Convert Turn[] to DeepSeek API message format
    const messages = this.convertTurnsToMessages(history);

    try {
      const response = await axios.post(
        DEEPSEEK_API_URL,
        {
          model: 'deepseek-chat', // Always use DeepSeek model name
          messages,
          stream: false,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.apiKey}`,
          },
        },
      );

      // Convert DeepSeek API response to GenerateContentResponse format
      return this.convertDeepSeekResponseToGeminiFormat(response.data);
    } catch (error: any) {
      if (error.response) {
        throw new Error(`API Error: ${error.response.data?.error?.message || error.response.statusText} (Status: ${error.response.status === 403 ? 'Forbidden' : error.response.status})`);
      }
      throw error;
    }
  }

  private convertTurnsToMessages(history: Turn[]): Array<{role: string, content: string}> {
    const messages: Array<{role: string, content: string}> = [];

    // Add a simple user message for now
    // In a real implementation, this would extract content from Turn objects
    messages.push({
      role: 'user',
      content: 'Hello, how can you help me?'
    });

    return messages;
  }

  private convertDeepSeekResponseToGeminiFormat(deepseekResponse: any): GenerateContentResponse {
    const choice = deepseekResponse.choices?.[0];
    const message = choice?.message;
    const content = message?.content || '';
    const toolCalls = message?.tool_calls;

    // Build parts array
    const parts: Part[] = [];

    // Add text content if available
    if (content) {
      parts.push({ text: content });
    }

    // Convert tool calls to function calls for the top-level functionCalls field
    const functionCalls: FunctionCall[] = [];
    if (toolCalls && Array.isArray(toolCalls)) {
      for (const toolCall of toolCalls) {
        if (toolCall.type === 'function' && toolCall.function) {
          const functionCall = {
            id: toolCall.id,
            name: toolCall.function.name,
            args: typeof toolCall.function.arguments === 'string'
              ? JSON.parse(toolCall.function.arguments)
              : toolCall.function.arguments
          };

          functionCalls.push(functionCall);

          // Also add to parts for compatibility
          parts.push({
            functionCall
          });
        }
      }
    }

    // Create response object with all fields
    const response: GenerateContentResponse = {
      candidates: [
        {
          content: {
            parts: parts.length > 0 ? parts : [{ text: content }],
            role: 'assistant',
          },
          finishReason: FinishReason.STOP,
          index: 0,
          safetyRatings: [],
        },
      ],
      usageMetadata: {
        promptTokenCount: deepseekResponse.usage?.prompt_tokens || 0,
        candidatesTokenCount: deepseekResponse.usage?.completion_tokens || 0,
        totalTokenCount: deepseekResponse.usage?.total_tokens || 0,
      },
      promptFeedback: {
        safetyRatings: [],
      },
      functionCalls: functionCalls.length > 0 ? functionCalls : undefined,
      text: undefined,
      data: undefined,
      executableCode: undefined,
      codeExecutionResult: undefined,
    };

    return response;
  }

  /**
   * Generate content with proper system prompt support
   * This method is called by the LLMProviderAdapter
   */
  async generateContentWithSystemPrompt(req: GenerateContentParameters): Promise<GenerateContentResponse> {
    const messages: Array<{role: string, content: string}> = [];

    // Add system prompt if provided
    if (req.config?.systemInstruction) {
      let systemContent = '';
      if (typeof req.config.systemInstruction === 'string') {
        systemContent = req.config.systemInstruction;
      } else if (Array.isArray(req.config.systemInstruction)) {
        // Handle PartUnion[]
        systemContent = req.config.systemInstruction
          .map((part: any) => typeof part === 'string' ? part : (part.text || ''))
          .join('');
      } else if (req.config.systemInstruction && 'parts' in req.config.systemInstruction) {
        // Handle Content object
        systemContent = (req.config.systemInstruction.parts || [])
          .map((part: any) => part.text || '')
          .join('');
      } else if (req.config.systemInstruction && 'text' in req.config.systemInstruction) {
        // Handle Part object
        systemContent = (req.config.systemInstruction as any).text || '';
      }

      if (systemContent.trim()) {
        messages.push({
          role: 'system',
          content: systemContent
        });
      }
    }

    // Add conversation history from contents
    if (req.contents) {
      // Convert ContentListUnion to Content[]
      const contentsArray = Array.isArray(req.contents) ? req.contents : [req.contents];

      for (const contentItem of contentsArray) {
        // Handle different content types
        let content: any;
        if (typeof contentItem === 'string') {
          content = { role: 'user', parts: [{ text: contentItem }] };
        } else if (Array.isArray(contentItem)) {
          content = { role: 'user', parts: contentItem };
        } else if ('parts' in contentItem) {
          content = contentItem;
        } else {
          content = { role: 'user', parts: [contentItem] };
        }

        const role = content.role === 'model' ? 'assistant' : content.role;
        const text = (content.parts || [])
          .map((part: any) => part.text || '')
          .join('');

        if (text.trim()) {
          messages.push({
            role,
            content: text
          });
        }
      }
    }

    // Convert tools to DeepSeek format if provided
    let tools: any[] | undefined;
    if (req.config?.tools && Array.isArray(req.config.tools) && req.config.tools.length > 0) {
      tools = this.convertGeminiToolsToDeepSeekFormat(req.config.tools);
    }

    try {
      const requestBody: any = {
        model: 'deepseek-chat',
        messages,
        stream: false,
        temperature: req.config?.temperature || 0,
        top_p: req.config?.topP || 1,
        max_tokens: req.config?.maxOutputTokens || 8192,
      };

      // Add tools if available
      if (tools && tools.length > 0) {
        requestBody.tools = tools;
      }



      const response = await axios.post(
        DEEPSEEK_API_URL,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${this.apiKey}`,
          },
          signal: req.config?.abortSignal,
        },
      );

      return this.convertDeepSeekResponseToGeminiFormat(response.data);
    } catch (error: any) {
      if (error.response?.status === 403) {
        throw new Error(
          `DeepSeek API authentication failed. ` +
          `Environment variable DEEPSEEK_API_KEY: ${process.env.DEEPSEEK_API_KEY ? 'SET' : 'NOT SET'}. ` +
          `Please check your API key.`
        );
      }
      if (error.response) {
        throw new Error(`DeepSeek API Error: ${error.response.data?.error?.message || error.response.statusText} (Status: ${error.response.status})`);
      }
      throw error;
    }
  }

  /**
   * Generate content from Content array (used for tool calls)
   * This method is compatible with GeminiClient.generateContentFromContent
   */
  async generateContentFromContent(
    contents: Content[],
    generationConfig: GenerateContentConfig,
    abortSignal: AbortSignal,
    tools?: any[]
  ): Promise<GenerateContentResponse> {
    // Import getCoreSystemPrompt dynamically to avoid circular dependency
    const { getCoreSystemPrompt } = await import('./prompts.js');

    const userMemory = this.config.getUserMemory();
    const systemInstruction = getCoreSystemPrompt(userMemory);

    // Create GenerateContentParameters object
    const req: GenerateContentParameters = {
      model: 'deepseek-chat',
      contents,
      config: {
        ...generationConfig,
        systemInstruction,
        abortSignal,
        tools: tools || generationConfig?.tools
      }
    };

    return this.generateContentWithSystemPrompt(req);
  }

  /**
   * Convert Gemini tools format to DeepSeek tools format
   */
  private convertGeminiToolsToDeepSeekFormat(geminiTools: any[]): any[] {
    return geminiTools.map((tool: any) => {
      if (tool.functionDeclarations) {
        // Handle Gemini function declarations
        return tool.functionDeclarations.map((func: any) => ({
          type: 'function',
          function: {
            name: func.name,
            description: func.description,
            parameters: func.parameters || { type: 'object', properties: {} }
          }
        }));
      }
      return tool;
    }).flat();
  }

}
