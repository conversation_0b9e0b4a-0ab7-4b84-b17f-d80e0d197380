#!/usr/bin/env node

// Simple test to check if GEMINI.md exists and can be read
import * as fs from 'fs';

async function testGeminiMdContent() {
  console.log('🧪 Testing GEMINI.md content...');

  try {
    // Check if GEMINI.md exists
    if (fs.existsSync('GEMINI.md')) {
      console.log('✅ GEMINI.md file exists');

      const content = fs.readFileSync('GEMINI.md', 'utf8');
      console.log('📝 GEMINI.md content length:', content.length);
      console.log('📝 GEMINI.md content preview (first 500 chars):');
      console.log(content.substring(0, 500));
      console.log('📝 Contains "Building and running":', content.includes('Building and running'));
      console.log('📝 Contains "Vitest":', content.includes('Vitest'));
    } else {
      console.log('❌ GEMINI.md file does not exist');
    }

    console.log('✅ Test completed');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testGeminiMdContent();
